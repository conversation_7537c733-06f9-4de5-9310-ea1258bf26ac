/**
 * <PERSON><PERSON> script to test the Telegram integration's reply detection functionality
 * This script demonstrates how the TelegramIntegration class handles replies to bot messages
 */

import { TelegramIntegration } from "../mastra/integrations/telegram";

// Mock implementation for demonstration
class MockTelegramIntegration extends TelegramIntegration {
  // Make private methods accessible for testing
  public async testIsReplyingToBotMessage(msg: any): Promise<boolean> {
    return (this as any).isReplyingToBotMessage(msg);
  }

  public testIsBotMentioned(msg: any): boolean {
    return (this as any).isBotMentioned(msg);
  }

  public testIsGroupChat(chatType: string): boolean {
    return (this as any).isGroupChat(chatType);
  }
}

async function demonstrateReplyDetection() {
  console.log("🤖 Telegram Reply Detection Demo\n");

  // Create a mock instance (won't actually connect to Telegram)
  const mockIntegration = new MockTelegramIntegration("demo-token");

  // Test 1: Group chat with bot mention
  console.log("📝 Test 1: Group Chat with Bot Mention");
  const mentionMessage = {
    chat: { type: "group" },
    text: "@testbot hello there",
    entities: [
      {
        type: "mention",
        offset: 0,
        length: 8
      }
    ],
    reply_to_message: null
  };
  
  const isGroupChat = mockIntegration.testIsGroupChat(mentionMessage.chat.type);
  const isMentioned = mockIntegration.testIsBotMentioned(mentionMessage);
  
  console.log(`Is group chat: ${isGroupChat}`);
  console.log(`Bot mentioned: ${isMentioned}`);
  console.log(`Should respond: ${isGroupChat && isMentioned}\n`);

  // Test 2: Group chat with reply to bot (no mention)
  console.log("📝 Test 2: Group Chat Reply to Bot (No Mention)");
  const replyMessage = {
    chat: { type: "group" },
    text: "Thanks for the help!",
    entities: null,
    reply_to_message: {
      from: { id: 123456789 } // This would be the bot's ID in real scenario
    }
  };
  
  const isGroupChat2 = mockIntegration.testIsGroupChat(replyMessage.chat.type);
  const isMentioned2 = mockIntegration.testIsBotMentioned(replyMessage);
  
  console.log(`Is group chat: ${isGroupChat2}`);
  console.log(`Bot mentioned: ${isMentioned2}`);
  console.log(`Has reply_to_message: ${!!replyMessage.reply_to_message}`);
  console.log(`Should respond: ${isGroupChat2 && (isMentioned2 || !!replyMessage.reply_to_message)}\n`);

  // Test 3: Group chat with regular message (should be ignored)
  console.log("📝 Test 3: Group Chat Regular Message (Should be Ignored)");
  const regularMessage = {
    chat: { type: "group" },
    text: "Just a regular message",
    entities: null,
    reply_to_message: null
  };
  
  const isGroupChat3 = mockIntegration.testIsGroupChat(regularMessage.chat.type);
  const isMentioned3 = mockIntegration.testIsBotMentioned(regularMessage);
  
  console.log(`Is group chat: ${isGroupChat3}`);
  console.log(`Bot mentioned: ${isMentioned3}`);
  console.log(`Has reply_to_message: ${!!regularMessage.reply_to_message}`);
  console.log(`Should respond: ${isGroupChat3 && (isMentioned3 || !!regularMessage.reply_to_message)}\n`);

  // Test 4: Private chat (should always respond)
  console.log("📝 Test 4: Private Chat (Should Always Respond)");
  const privateMessage = {
    chat: { type: "private" },
    text: "Hello in private",
    entities: null,
    reply_to_message: null
  };
  
  const isGroupChat4 = mockIntegration.testIsGroupChat(privateMessage.chat.type);
  
  console.log(`Is group chat: ${isGroupChat4}`);
  console.log(`Should respond: ${!isGroupChat4} (always respond in private chats)\n`);
}

// Run the demo
if (require.main === module) {
  demonstrateReplyDetection().catch(console.error);
}

export { demonstrateReplyDetection };
