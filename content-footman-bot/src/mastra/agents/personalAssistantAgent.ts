import { google } from '@ai-sdk/google';
import { Agent } from "@mastra/core/agent";
import { weatherTool, fetchProductImageTool, searchAndGetProductDetailsTool } from "../tools";
import { Memory } from "@mastra/memory";
import { MCPClient } from "@mastra/mcp";
import path from "path";
import { LibSQLStore } from "@mastra/libsql";
import { dailyWorkflow } from "../workflows";

const mcp = new MCPClient({
  servers: {
    hackernews: {
      command: "npx",
      args: ["-y", "@devabdultech/hn-mcp-server"],
    },
    textEditor: {
      command: "pnpx",
      args: [
        `@modelcontextprotocol/server-filesystem`,
        path.join(process.cwd(), "../", "../", "notes"),
      ],
    },
  },
});

const mcpTools = await mcp.getTools();

const memory = new Memory({
  storage: new LibSQLStore({
    url: "file:../mastra.db", // Or your database URL
  }),
  options: {
    // Keep last 20 messages in context
    lastMessages: 20,
    workingMemory: {
      enabled: true,
      template: `<user>
          <first_name></first_name>
          <username></username>
          <preferences></preferences>
          <interests></interests>
          <conversation_style></conversation_style>
        </user>`,
    },
  },
});

export const personalAssistantAgent = new Agent({
  name: "Personal Assistant",
  instructions: `
      You are a helpful personal assistant that can help with providing weather information,
      and searching for Amazon products.

      You have access to the following tools:

      ## Weather:
         - Use this tool for getting weather information for specific locations
         - It can provide details like temperature, humidity, wind conditions, and weather conditions
         - Always ask for the location or if it's not provided try to use your working memory
           to get the user's last requested location

      ## Hackernews:
         - Use this tool to search for stories on Hackernews
         - You can use it to get the top stories or specific stories
         - You can use it to retrieve comments for stories

      ## Amazon Product Search:
         - Use "fetch-product-image" to get product images
         - Use "search-and-get-product-details" for a comprehensive search that automatically
           fetches detailed information for each product in one step (recommended for most use cases)
         - These tools are great for product research, price comparison, and shopping assistance
         - Alway attach purchase urls and product images

      ## Filesystem:
         - You also have filesystem read/write access to a notes directory.
         - You can use that to store information such as reminders for later use or organize info for the user.
         - You can use this notes directory to keep track of to do list items for the user.
         - Notes dir: ${path.join(process.cwd(), `notes`)}
  `,
  model: google("gemini-2.5-flash"),
  tools: {
    ...mcpTools,
    weatherTool,
    fetchProductImageTool,
    searchAndGetProductDetailsTool,
  },
  workflows: {
    dailyWorkflow,
  },
  memory,
});
