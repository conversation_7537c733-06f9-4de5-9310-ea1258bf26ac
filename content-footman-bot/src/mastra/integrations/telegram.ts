import TelegramBot from "node-telegram-bot-api";
import { personalAssistantAgent } from "../agents/personalAssistantAgent";
import { messageSplitterAgent } from "../agents/messageSplitterAgent";

export class TelegramIntegration {
  private bot: TelegramBot;
  private readonly MAX_MESSAGE_LENGTH = 4096; // Telegram's message length limit
  private readonly MAX_RESULT_LENGTH = 500; // Maximum length for tool results
  private botUsername: string | null = null;

  constructor(token: string) {
    // Create a bot instance
    this.bot = new TelegramBot(token, { polling: true });

    // Get bot information to store username
    this.initializeBotInfo();

    // Handle incoming messages
    this.bot.on("message", this.handleMessage.bind(this));
  }

  private async initializeBotInfo() {
    try {
      const botInfo = await this.bot.getMe();
      this.botUsername = botInfo.username?.toLowerCase() || null;
      console.log(`Bot initialized: @${botInfo.username}`);
    } catch (error) {
      console.error("Failed to get bot info:", error);
    }
  }

  private escapeMarkdown(text: string): string {
    // Escape special Markdown characters
    return text.replace(/[_*[\]()~`>#+=|{}.!-]/g, "\\$&");
  }

  private truncateString(str: string, maxLength: number): string {
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength) + "... [truncated]";
  }

  private formatToolResult(result: any): string {
    try {
      const jsonString = JSON.stringify(result, null, 2);
      return this.escapeMarkdown(
        this.truncateString(jsonString, this.MAX_RESULT_LENGTH)
      );
    } catch (error) {
      return `[Complex data structure - ${typeof result}]`;
    }
  }

  private async splitMessageWithLLM(content: string): Promise<string[]> {
    try {
      // Use the personal assistant agent to split the message
      const splitPrompt = `
Please split the following message into smaller parts
Content to split: ${content}
Return only the JSON array, no other text.
`;

      console.log("Splitting message:", content);

      const response = await messageSplitterAgent.generate(splitPrompt);

      // parse response
      console.log("Split response:", response.text);


      // Extract JSON from markdown code block if present
      const jsonMatch = response.text.match(/```json\n([\s\S]*?)\n```/);
      let jsonString = response.text;
      if (jsonMatch && jsonMatch[1]) {
        jsonString = jsonMatch[1];
      }

      const parts = JSON.parse(jsonString);
      if (Array.isArray(parts)) {
        return parts.filter(part => typeof part === 'string' && part.trim().length > 0);
      }
    } catch (error) {
      console.error("Error splitting message with LLM:", error);
    }

    // Fallback: simple character-based splitting
    return this.fallbackSplitMessage(content);
  }

  private fallbackSplitMessage(content: string): string[] {
    const parts: string[] = [];
    let currentPart = "";
    const lines = content.split('\n');

    for (const line of lines) {
      if (currentPart.length + line.length + 1 > this.MAX_MESSAGE_LENGTH - 100) {
        if (currentPart.trim()) {
          parts.push(currentPart.trim());
        }
        currentPart = line;
      } else {
        currentPart += (currentPart ? '\n' : '') + line;
      }
    }

    if (currentPart.trim()) {
      parts.push(currentPart.trim());
    }

    return parts.length > 0 ? parts : [content];
  }

  private async updateOrSplitMessage(
    chatId: number,
    messageId: number | undefined,
    text: string
  ): Promise<number> {
    // If text is within limits, try to update existing message
    if (text.length <= this.MAX_MESSAGE_LENGTH && messageId) {
      try {
        await this.bot.editMessageText(text, {
          chat_id: chatId,
          message_id: messageId,
          parse_mode: "MarkdownV2",
        });
        return messageId;
      } catch (error) {
        console.error("Error updating message:", error);
      }
    }

    // If text is too long or update failed, send as new message
    try {
      const newMessage = await this.bot.sendMessage(chatId, text, {
        parse_mode: "MarkdownV2",
      });
      return newMessage.message_id;
    } catch (error) {
      console.error("Error sending message:", error);
      // If the message is still too long, truncate it
      const truncated =
        text.substring(0, this.MAX_MESSAGE_LENGTH - 100) +
        "\n\n... [Message truncated due to length]";
      const fallbackMsg = await this.bot.sendMessage(chatId, truncated, {
        parse_mode: "MarkdownV2",
      });
      return fallbackMsg.message_id;
    }
  }

  private isGroupChat(chatType: string): boolean {
    return chatType === "group" || chatType === "supergroup";
  }

  private isBotMentioned(msg: TelegramBot.Message): boolean {
    // Check if the message has entities and if any of them are mentions
    if (!msg.entities || !msg.text) {
      return false;
    }

    // Get bot information to check for mentions
    return msg.entities.some(entity => {
      if (entity.type === "mention") {
        // Extract the mentioned username from the message text
        const mentionText = msg.text!.substring(entity.offset, entity.offset + entity.length);
        // Remove the @ symbol and compare with bot username
        const mentionedUsername = mentionText.substring(1).toLowerCase();

        // Compare with the bot's username
        return this.botUsername !== null && mentionedUsername === this.botUsername;
      }
      return false;
    });
  }

  private async isReplyingToBotMessage(msg: TelegramBot.Message): Promise<boolean> {
    // Check if this message is a reply to another message
    if (!msg.reply_to_message) {
      return false;
    }

    // Check if the replied-to message is from the bot
    try {
      const botInfo = await this.bot.getMe();
      return msg.reply_to_message.from?.id === botInfo.id;
    } catch (error) {
      console.error("Error checking if reply is to bot message:", error);
      return false;
    }
  }

  private async handleMessage(msg: TelegramBot.Message) {
    const chatId = msg.chat.id;
    const text = msg.text;
    const username = msg.from?.username || "unknown";
    const firstName = msg.from?.first_name || "unknown";
    const userId = msg.from?.id.toString() || `anonymous-${chatId}`;

    if (!text) {
      await this.bot.sendMessage(
        chatId,
        "Sorry, I can only process text messages."
      );
      return;
    }

    // Check if this is a group chat and if the bot is mentioned or replied to
    if (this.isGroupChat(msg.chat.type)) {
      const isMentioned = this.isBotMentioned(msg);
      const isReplyingToBot = await this.isReplyingToBotMessage(msg);
      console.log(`Group chat message received. Bot mentioned: ${isMentioned}, Replying to bot: ${isReplyingToBot}`);

      if (!isMentioned && !isReplyingToBot) {
        // In group chats, only respond when the bot is mentioned or when replying to bot's message
        console.log("Ignoring group message without bot mention or reply to bot");
        return;
      }

      if (isMentioned) {
        console.log("Responding to group message with bot mention");
      } else if (isReplyingToBot) {
        console.log("Responding to group message replying to bot");
      }
    } else {
      console.log(`Private chat message received from ${firstName}`);
    }

    try {
      // Send initial message
      const sentMessage = await this.bot.sendMessage(chatId, "Thinking...");
      let currentResponse = ""; // Only for main text content
      let progressMessages: string[] = []; // For tool calls, reasoning, etc.
      let currentMessageId = sentMessage.message_id;

      // Stream response using the agent
      const stream = await personalAssistantAgent.stream(text, {
        threadId: `telegram-${chatId}`, // Use chat ID as thread ID
        resourceId: userId, // Use user ID as resource ID
        context: [
          {
            role: "system",
            content: `Current user: ${firstName} (${username})`,
          },
        ],
      });

      // Process the full stream
      for await (const chunk of stream.fullStream) {
        let shouldSendProgress = false;
        let chunkText = "";
        let progressText = "";

        switch (chunk.type) {
          case "text-delta":
            chunkText = this.escapeMarkdown(chunk.textDelta);
            currentResponse += chunkText;
            break;

          case "tool-call":
            const formattedArgs = JSON.stringify(chunk.args, null, 2);
            progressText = `🛠️ Using tool: ${this.escapeMarkdown(
              chunk.toolName
            )}\nArguments:\n\`\`\`\n${this.escapeMarkdown(
              formattedArgs
            )}\n\`\`\``;
            console.log(`Tool call: ${chunk.toolName}`, chunk.args);
            progressMessages.push(progressText);
            shouldSendProgress = true;
            break;

          case "tool-result":
            const formattedResult = this.formatToolResult(chunk.result);
            progressText = `✨ Result:\n\`\`\`\n${formattedResult}\n\`\`\``;
            console.log("Tool result:", chunk.result);
            progressMessages.push(progressText);
            shouldSendProgress = true;
            break;

          case "error":
            progressText = `❌ Error: ${this.escapeMarkdown(
              String(chunk.error)
            )}`;
            console.error("Error:", chunk.error);
            progressMessages.push(progressText);
            shouldSendProgress = true;
            break;

          case "reasoning":
            // Skip reasoning in live updates to reduce noise
            console.log("Reasoning:", chunk.textDelta);
            break;

          case "finish":
            // Only split and send the main response content (not progress messages)
            if (currentResponse.trim()) {
              const messageParts = await this.splitMessageWithLLM(currentResponse);
              try {
                await this.bot.deleteMessage(chatId, currentMessageId);
              } catch (error) {
                console.error("Error deleting thinking message:", error);
              }
              for (const part of messageParts) {
                try {
                  await this.bot.sendMessage(chatId, part, {
                    // parse_mode: "MarkdownV2",
                  });
                  // Small delay between messages to avoid rate limits
                  await new Promise(resolve => setTimeout(resolve, 100));
                } catch (error) {
                  console.error("Error sending split message:", error);
                  // Fallback: send without markdown
                  try {
                    await this.bot.sendMessage(chatId, part);
                  } catch (fallbackError) {
                    console.error("Error sending fallback message:", fallbackError);
                  }
                }
              }
            }
            break;
        }

        // Handle progress messages (tool calls, results, errors)
        if (shouldSendProgress) {
          try {
            await this.bot.editMessageText(progressMessages.join("\n\n"), {
              chat_id: chatId,
              message_id: currentMessageId,
              parse_mode: "MarkdownV2",
            });
          } catch (error) {
            console.error("Error sending progress message:", error);
            // Fallback without markdown
            try {
              await this.bot.editMessageText(progressMessages.join("\n\n"), {
                chat_id: chatId,
                message_id: currentMessageId,
              });
            } catch (fallbackError) {
              console.error("Error sending fallback progress message:", fallbackError);
            }
          }
        }
      }
    } catch (error) {
      console.error("Error processing message:", error);
      await this.bot.sendMessage(
        chatId,
        "Sorry, I encountered an error processing your message. Please try again."
      );
    }
  }
}
